<script lang="ts" src="./event.ts">
</script>

<style lang="sass">
@import './event.scss'
</style>

<template>
    <div id="user-ticketing-page" class="scrolling-page">
        <div class="centered-content">
            <div class="main-content">

                <div class="block">
                    <img class="event-banner" :src="getEventBannerUrl(platformState.requireEvent)">

                    <h1 class="event-name"> {{ platformState.requireEvent.name }}</h1>

                    <div class="when" v-if="platformState.requireEvent.startingDate && !platformState.requireEvent.endingDate">
                        <div class="calendar">
                            <div class="day"> {{ $filters.DayNumber(platformState.requireEvent.startingDate) }} </div>
                            <div class="month"> {{ $filters.ShortMonth(platformState.requireEvent.startingDate) }} </div>
                        </div>

                        <div class="readable-date">
                            <span class="date"> {{ $filters.Day(platformState.requireEvent.startingDate) }} {{ $filters.Year(platformState.requireEvent.startingDate) }} </span>
                            <span class="time" v-if="platformState.requireEvent.startingTime"> à partir de {{ platformState.requireEvent.startingTime.substring(0, 5) }} </span>
                            <span class="time" v-else> Toute la journée </span>
                        </div>
                    </div>
                    <div class="when" v-else-if="platformState.requireEvent.startingDate && platformState.requireEvent.endingDate">
                        <div class="calendar">
                            <div class="day"> {{ $filters.DayNumber(platformState.requireEvent.startingDate) }} </div>
                            <div class="month"> {{ $filters.ShortMonth(platformState.requireEvent.startingDate) }} </div>
                        </div>

                        <div class="readable-date">
                            <span class="date">
                                Du {{ $filters.Day(platformState.requireEvent.startingDate) }} {{ $filters.Year(platformState.requireEvent.startingDate) }}
                                au {{ $filters.Day(platformState.requireEvent.endingDate) }} {{ $filters.Year(platformState.requireEvent.endingDate) }}
                            </span>
                            <span class="time" v-if="platformState.requireEvent.startingTime"> à partir de {{ platformState.requireEvent.startingTime.substring(0, 5) }} </span>
                            <span class="time" v-else> Toute la journée </span>
                        </div>
                    </div>

                    <span class="from"> proposé par <span> {{ platformState.requireEvent.establishment.name }}</span> </span>
                </div>

                <div class="block" v-if="platformState.requireEvent.description">
                    <h2> Description </h2>

                    <div class="event-description" v-html="getEventDescription()"></div>

                    <div class="read-more" @click="showFullDescription = !showFullDescription">
                        {{ showFullDescription ? 'Voir moins' : 'Voir plus' }}
                    </div>
                </div>

<!--                <div class="block">-->
<!--                    <h2> Galerie </h2>-->

<!--                    <div class="image-carrousel">-->
<!--                        <img src="https://images.lanouvellerepublique.fr/image/upload/c_limit,w_980/f_auto/q_auto/6087d890e663b07f5c8b45a9.jpg?_a=BAVAfVIB0" />-->
<!--                        <img src="https://aladebauche.com/read/image/none/13372" />-->
<!--                        <img src="https://images.lanouvellerepublique.fr/image/upload/620bd8ed6d8f55fd038b461c" />-->
<!--                    </div>-->
<!--                </div>-->

                <div class="block" v-if="platformState.requireEvent.location">
                    <h2> Lieu </h2>

                    <div class="location">
                        <i class="fa-regular fa-location-dot"></i>
                        <span>
                            {{ platformState.requireEvent.location.line1 }},
                            {{ platformState.requireEvent.location.postalCode }}
                            {{ platformState.requireEvent.location.city }}
                        </span>
                    </div>
                </div>

                <div class="block">
                    <h2> Proposé par </h2>

                    <div class="contact">
                        <div class="establishment">
                            <div class="icon">
                                <i class="fa-regular fa-building"></i>
                            </div>

                            <div class="infos">
                                <span class="name"> {{ platformState.requireEvent.establishment.name }}</span>
                                <span class="other"> 1 autre événement </span>
                            </div>

                        </div>

<!--                        <button class="small grey button" @click="showContactModal = true"> Contacter </button>-->
                    </div>
                </div>

                <div class="block">
                    <h2> Aide </h2>

                    <div class="help">
                        <span> J'ai perdu mon billet, comment puis-je le récupérer ? </span>
<!--                        <span> Comment faire pour faire ca ? </span>-->
                    </div>
                </div>
            </div>

            <div class="desktop-tickets">
                <div class="sticky-container">
                    <h3> Billets </h3>

                    <div v-if="onSaleProducts.length === 0">
                        Aucun ticket dans cet événement
                    </div>

                    <div class="tickets">
                        <template v-for="stockTemporal in platformState.requireEvent.stockTemporalList">
                            <temporal-stock-form :stock-temporal="stockTemporal"></temporal-stock-form>
                        </template>

						<template v-for="product in platformState.requireEvent.products">
							<product-ticket :product="product" v-if="platformState.isProductOnSale(product) && !platformState.haveTemporalStock(product)"></product-ticket>
						</template>

                        <a :href="checkoutUrl" class="add-to-cart button" :class="{disabled: orderManager.purchasesQuantityInOrder === 0}">
                            <span class="amount" v-if="orderManager.purchasesQuantityInOrder > 0">
                                ({{ orderManager.purchasesQuantityInOrder }})
                                {{ $filters.Money(orderManager.orderTotals.withTaxesAfterDiscount) }}
                            </span>
                            Réserver
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="credits">
            <a target="_blank" href="https://weecop.fr/"> Billetterie par Weecop </a>
            <a v-if="platformState.requireEvent.ecommerceConfig.sellerTosLink" target="_blank" :href="platformState.requireEvent.ecommerceConfig.sellerTosLink"> Conditions du vendeur </a>
            <a v-if="platformState.requireEvent.ecommerceConfig.platformTosLink" target="_blank" :href="platformState.requireEvent.ecommerceConfig.platformTosLink"> Conditions générales d'utilisation </a>
            <a target="_blank" :href="`https://littl.fr/mastodon-report?event=${platformState.requireEvent.uid}/`">
                <i class="fa-regular fa-flag"></i>
                Signaler cet événement
            </a>
        </div>

        <div class="tickets-cta">
            <div class="left">
                <template v-if="getCheaperAvailableProduct() !== null">
                    <span class="small"> à partir de </span>
                    <span class="big"> {{ $filters.Money(getCheaperAvailableProduct() ?? 0) }} </span>
                </template>
            </div>

            <a :href="mobileTicketsUrl" class="button"> Voir les billets </a>
        </div>
    </div>

    <form-modal-or-drawer
        title="Contacter l'organisateur"
        subtitle="Une question ou besoin d'un renseignement ?"
        :state="showContactModal"
        @close="showContactModal = false"
    >
        <template v-slot:content>
            <div class="input-group">
                <label> Votre email </label>

                <div class="fluid input">
                    <input class="fluid" placeholder="Email" />
                </div>
            </div>

            <div class="input-group">
                <label> Demande </label>

                <div class="fluid input">
                    <textarea class="fluid" placeholder="Votre question ou renseignement..."></textarea>
                </div>
            </div>
        </template>

        <template v-slot:buttons>
            <button type="button" class="white button" @click="showContactModal = false"> Annuler </button>
            <button type="button" class="button">
                Envoyer
            </button>
        </template>
    </form-modal-or-drawer>
</template>