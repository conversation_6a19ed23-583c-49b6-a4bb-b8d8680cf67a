import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {AirtableRecord} from "../../packageScan";
import {BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";
import CameraComponent from "../CameraComponent/CameraComponent.vue";
import ItemActionsComponent from "../ItemActionsComponent/ItemActionsComponent.vue";
import {ItemAction} from "../ItemActionsComponent/ItemActionsComponent";
import ImageGalleryComponent from "../ImageGalleryComponent/ImageGalleryComponent.vue";
import ObjectFormComponent from "../ObjectFormComponent/ObjectFormComponent.vue";

@Component({
    components: {
        'camera': CameraComponent,
        'item-actions': ItemActionsComponent,
        'image-gallery': ImageGalleryComponent,
        'object-form': ObjectFormComponent
    },
    emits: ['close', 'updated', 'updated-object']
})
export default class PackageValidationComponent extends Vue {
    @Prop({required: true}) packageData!: AirtableRecord;
    @Prop({required: true}) objectsInPackage!: AirtableRecord[];
    @Prop({required: true}) objectTranslations!: AirtableRecord[];

    notIn: AirtableRecord|null = null;
    scanState: 'ERROR'|'SUCCESS'|null = null;

    showActionsForObject: AirtableRecord|null = null;
    showCamera: { opened: boolean, forObject: AirtableRecord }|null = null;
    showObjectImage: AirtableRecord|null = null;
    showObjectForm: AirtableRecord|null = null;
    showObjectReturnImage: AirtableRecord|null = null;

    scanning: boolean = false;
    saving: boolean = false;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        this.setupListener();
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    get notScannedObjects() {
        return this.objectsInPackage.filter((object) => !object.fields.return);
    }

    get scannedObjects() {
        return this.objectsInPackage.filter((object) => object.fields.return);
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        if(this.scanning) return;
        this.scanning = true;

        const correspondingAirtableObject = this.isIn(data.content);
        if(correspondingAirtableObject === null) {
            this.notIn = correspondingAirtableObject; // Not used for now
            this.scanState = 'ERROR';
        }
        else {
            if(correspondingAirtableObject.fields.return) {
                this.scanState = 'SUCCESS';
            } else {
                const response = await this.validateObject(correspondingAirtableObject);

                if(response instanceof FetchError) {
                    this.scanState = 'ERROR';
                } else {
                    this.scanState = 'SUCCESS';
                }
            }
        }

        setTimeout(() => this.scanState = null, 200);

        this.scanning = false;
    }

    isIn(objectId: string): AirtableRecord|null {
        return this.objectsInPackage.find((object) => object.fields.code === objectId) ?? null;
    }

    async validateObject(object: AirtableRecord, validate: boolean = true) {
        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": object.id,
                        "fields": {
                            "return": validate,
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.$emit('updated-object', json.records[0]);
        }

        return response;
    }

    async validatePackage() {
        this.saving = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": this.packageData.id,
                        "fields": {
                            "name": this.packageData.fields.name,
                            "shipping": this.packageData.fields.shipping,
                            "return": this.packageData.fields.return,
                            "validated": true
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.updateObject(json.records[0]);
        }

        this.saving = false;
        this.close();
    }

    getObjectTranslation(objectId: string) {
        const translationObject = this.objectTranslations.find((object) => object.fields.code === objectId);
        if(translationObject) {
            return translationObject.fields.brand + ' ' + translationObject.fields.name + ' - ' + objectId;
        } else {
            return objectId;
        }
    }

    async actionClicked(action: ItemAction) {
        if(!this.showActionsForObject) return;
        if(action.id === 'take-picture') {
            this.showCamera = {opened: true, forObject: this.showActionsForObject};
        } else if(action.id === 'show-return-image') {
            this.showObjectReturnImage = this.showActionsForObject;
        } else if(action.id === 'show-image') {
            this.showObjectImage = this.showActionsForObject;
        } else if(action.id === 'validate') {
            this.scanning = true;
            await this.validateObject(this.showActionsForObject);
            this.scanning = false;
        } else if(action.id === 'unvalidate') {
            this.scanning = true;
            await this.validateObject(this.showActionsForObject, false);
            this.scanning = false;
        }else if(action.id === 'edit') {
            this.showObjectForm = this.showActionsForObject;
        }
    }

    getObjectActions(objectData: AirtableRecord) {
        const actions: ItemAction[] = [
            {id: 'take-picture', icon: 'fa-regular fa-camera', text: 'Image de retour'},
            {id: 'edit', icon: 'fa-regular fa-pen-line', text: 'Modifier'},
        ];

        if(objectData.fields.returnImages) {
            actions.unshift({id: 'show-return-image', icon: 'fa-regular fa-image', text: 'Visualiser les images de retour'});
        }

        if(objectData.fields.images) {
            actions.unshift({id: 'show-image', icon: 'fa-regular fa-image', text: 'Visualiser les images'});
        }

        if(objectData.fields.return) {
            actions.unshift({id: 'unvalidate', icon: 'fa-regular fa-check', text: 'Dé-valider l\'objet'});
        } else {
            actions.unshift({id: 'validate', icon: 'fa-regular fa-check', text: 'Valider l\'objet'});
        }

        return actions;
    }


    async photoCaptured(dataUrl: string) {
        if(!this.showCamera) return;
        this.scanning = true;

        // Extract image type from dataUrl for filename
        const imageType = dataUrl.split(';')[0].split('/')[1] || 'png';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `captured_image_${timestamp}.${imageType}`;

        const responseFile = await genericFetch({
            url: `https://content.airtable.com/v0/app9UWPLidwgXu3jB/${this.showCamera.forObject.id}/returnImages/uploadAttachment`,
            method: 'POST',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "contentType": "image/" + imageType,
                "file": dataUrl,
                "filename": filename
            })
        });

        if(responseFile instanceof FetchError) {
        } else {}


        this.showCamera = null;
        this.scanning = false;
    }

    updateObject(objectData: AirtableRecord) {
        this.$emit('updated-object', objectData);
    }

    close() {
        this.$emit('close');
    }
}