import {
    OnlineOrderExecutorModel,
    PublicEventApiOut, PublicTemplateTicketApiOut,
    UuidScopeProduct_event, uuidScopeProduct_stockTemporal,
    UuidScopeProduct_stockTemporal, UuidScopeProduct_templateTicket
} from "@groupk/mastodon-core";
import {ScopedUuid, SetInstance, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {PublicProductApiOut} from "@groupk/mastodon-core";

export class PlatformState {
    eventUid: VisualScopedUuid<UuidScopeProduct_event>|null = null;

    private _event: PublicEventApiOut|null = null;

    get requireUrlEventUid(){
        if(!this.eventUid) throw new Error('no_registered_event_uid');
        return this.eventUid;
    }

    get event(): PublicEventApiOut|null {
        return this._event;
    }

    registerEvent(event: PublicEventApiOut) {
        this._event = event;
        const publicOrderExecutorModel = new OnlineOrderExecutorModel({
            billingRegions: event.billingRegions
        });
        for(let product of event.products) {
            publicOrderExecutorModel.registerProduct(product);
        }
        SetInstance(OnlineOrderExecutorModel, publicOrderExecutorModel);
    }

    get requireEvent(){
        if(!this._event) throw new Error('no_registered_event');
        return this._event;
    }

    isProductOnSale(product: PublicProductApiOut) {
        const date = new Date().toISOString();
        if(product.onSaleStart && date < product.onSaleStart) return false;
        if(product.onSaleEnd && date > product.onSaleEnd) return false;
        return product.onSale;
    }

    haveTemporalStock(product: PublicProductApiOut) {
        return product.stockUid !== null && UuidUtils.isVisual<UuidScopeProduct_stockTemporal>(product.stockUid, uuidScopeProduct_stockTemporal);
    }

    requireTemplateTicketWithUid(templateTicketUid: VisualScopedUuid<UuidScopeProduct_templateTicket>): PublicTemplateTicketApiOut {
        const templateTicket = this.requireEvent.templateTicketList.find((templateTicket) => templateTicket.uid === templateTicketUid);
        if(!templateTicket) throw new Error('missing_template_ticket');
        return templateTicket;
    }
}