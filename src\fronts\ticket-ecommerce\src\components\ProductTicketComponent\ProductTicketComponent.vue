<script lang="ts" src="./ProductTicketComponent.ts">
</script>

<style lang="sass">
@import './ProductTicketComponent.scss'
</style>

<template>
    <div class="product-ticket-component">
        <span class="name"> {{ product.name }} </span>
        <span v-if="product.description" class="description">
            {{ product.description }}
        </span>

        <div class="bottom">
            <span class="price"> {{ $filters.Money(product.prices[0].withTaxes) }} </span>

            <div class="quantity-selector">
                <div class="minus" @click="orderManager.removeProductFromOrder(product)">
                    <i class="fa-regular fa-minus"></i>
                </div>

                <span class="quantity"> {{ orderManager.getProductQuantityInOrder(product) }} </span>

                <div class="plus" @click="orderManager.addProductToOrder(product)">
                    <i class="fa-regular fa-plus"></i>
                </div>
            </div>
        </div>
    </div>
</template>