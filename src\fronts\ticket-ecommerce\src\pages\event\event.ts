import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, buildHttpRoutePathWithArgs, UuidUtils} from "@groupk/horizon2-core";
import {MastodonHttpImagesContract, UuidScopeEstablishment} from "@groupk/mastodon-core";
import {AppBus} from "../../AppBus";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import ProductTicketComponent from "../../components/ProductTicketComponent/ProductTicketComponent.vue";
import {PublicEventApiOut} from "@groupk/mastodon-core";
import {Router} from "@groupk/horizon2-front";
import {PlatformState} from "../../PlatformState";
import {OrderManager} from "../../OrderManager";
import StockTemporalCalendarComponent
	from "../../components/StockTemporalCalendarComponent/StockTemporalCalendarComponent.vue";
import {AppState} from "../../../../../shared/AppState";
import {MainConfig} from "../../../../../shared/MainConfig";

@Component({
	components: {
		'form-modal-or-drawer': FormModalOrDrawerComponent,
		'product-ticket': ProductTicketComponent,
		'temporal-stock-form': StockTemporalCalendarComponent
	}
})
export default class EventView extends Vue {

	showFullDescription: boolean = false;
	showContactModal: boolean = false;

	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(PlatformState) accessor platformState!: PlatformState;
	@AutoWired(AppBus) accessor appBus!: AppBus;
	@AutoWired(OrderManager) accessor orderManager!: OrderManager;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;
	@AutoWired(Router) accessor router!: Router;

	mounted() {
		this.orderManager.on('updated', () => {
			this.$forceUpdate();
		});
	}

	get onSaleProducts() {
		return this.platformState.requireEvent.products.filter((product) => this.platformState.isProductOnSale(product));
	}

	load() {
		return {
			pageTitle: this.platformState.requireEvent.name + ' - Billets'
		};
	}


	getCheaperAvailableProduct() {
		let cheaperPrice: number|null = null;
		for(let product of this.platformState.requireEvent.products) {
			if(cheaperPrice === null || product.prices[0].withTaxes < cheaperPrice) {
				cheaperPrice =	product.prices[0].withTaxes;
			}
		}
		return cheaperPrice;
	}

	getEventDescription() {
		let description = this.platformState.requireEvent.description;
		if (!this.showFullDescription) {
			description = this.platformState.requireEvent.description.substring(0, 200);
			if(description.length === 200) description += '...';
		}
		return description;
	}

	getEventBannerUrl(event: PublicEventApiOut) {
		if(!event.mainImageUpload) return '';
		return this.mainConfig.configuration.mastodonApiEndpoint + buildHttpRoutePathWithArgs(MastodonHttpImagesContract.getPublicImage, {
			establishmentUid: this.appState.requireUrlEstablishmentUid(),
			imageId: event.mainImageUpload.uid,
			options: {},
			dimensions: {width: 800},
			extension: 'png',
			resizeType: 'contain',
			revision: event.mainImageUpload.lastUpdateDatetime,
		});
	}

	get mobileTicketsUrl() {
		return `/establishment/${UuidUtils.visualToScoped<UuidScopeEstablishment>(this.appState.requireUrlEstablishmentUid())}/event/${UuidUtils.visualToScoped(this.platformState.requireUrlEventUid)}/tickets`;
	}

	get checkoutUrl() {
		return `/establishment/${UuidUtils.visualToScoped<UuidScopeEstablishment>(this.appState.requireUrlEstablishmentUid())}/event/${UuidUtils.visualToScoped(this.platformState.requireUrlEventUid)}/checkout#form`;
	}
}