import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {AirtableRecord} from "../../packageScan";
import {BarcodeScannerManager} from "../../BarcodeScannerManager";

@Component({
    components: {},
    emits: ['close', 'created']
})
export default class PackageFormComponent extends Vue {
    @Prop({default: null}) editingPackage!: AirtableRecord|null;

    name: string = '';
    comment: string = '';
    shippingBarcode: string = '';
    returnBarcode: string = '';

    creating: boolean = false;

    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;

    mounted() {
        if(this.editingPackage) {
            this.name = this.editingPackage.fields.name;
            this.comment = this.editingPackage.fields.comment;
            this.shippingBarcode = this.editingPackage.fields.shipping;
            this.returnBarcode = this.editingPackage.fields.return;
        }

        this.barcodeScannerManager.customCallback = null;
    }

    save() {
        if(this.editingPackage) {
            this.update();
        } else {
            this.create();
        }
    }

    async create() {
        this.creating = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "fields": {
                            "name": this.name,
                            "comment": this.comment,
                            "shipping": this.shippingBarcode,
                            "return": this.returnBarcode
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.$emit('created', json.records[0]);
        }

        this.creating = false;
        this.close();
    }

    async update() {
        if(!this.editingPackage) return;
        this.creating = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package',
            method: 'PATCH',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "id": this.editingPackage.id,
                        "fields": {
                            "name": this.name,
                            "comment": this.comment,
                            "shipping": this.shippingBarcode,
                            "return": this.returnBarcode,
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.$emit('updated', json.records[0]);
        }

        this.creating = false;
        this.close();
    }

    close() {
        this.$emit('close');
    }
}