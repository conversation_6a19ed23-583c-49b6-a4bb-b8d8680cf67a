import {Component, Prop, Vue} from "vue-facing-decorator";
import {PublicProductApiOut} from "@groupk/mastodon-core";
import {GetInstance} from "@groupk/horizon2-core";
import {OrderManager} from "../../OrderManager";

@Component({})
export default class ProductTicketComponent extends Vue {
	@Prop({required: true}) product!: PublicProductApiOut;
	@Prop() customOrderManger!: OrderManager

	orderManager!: OrderManager;

	beforeMount() {
		if(this.customOrderManger) this.orderManager = this.customOrderManger;
		else this.orderManager = GetInstance(OrderManager);
	}

	mounted() {
		this.orderManager.on('updated', () => {
			this.$forceUpdate();
		})
	}
}