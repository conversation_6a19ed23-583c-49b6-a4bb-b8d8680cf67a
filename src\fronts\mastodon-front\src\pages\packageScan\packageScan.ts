import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, genericFetch} from "@groupk/horizon2-core";
import {FetchError} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import PackageComponent from "./components/PackageComponent/PackageComponent.vue";
import PackageFormComponent from "./components/PackageFormComponent/PackageFormComponent.vue";
import {BarcodeExternalNative, BarcodeExternalNative_QRCodeReaderReturn} from "@groupk/native-bridge";
import {BarcodeScannerManager} from "./BarcodeScannerManager";
import {EventListenerWrapper} from "@groupk/horizon2-core";

export interface AirtableRecord {
    id: string;
    fields: any;
    createdTime: string;
}

@Component({
    components: {
        'package-form': PackageFormComponent,
        'package': PackageComponent
    }
})
export default class packageScan extends Vue {
    existingPackages: AirtableRecord[] = [];
    objectTranslations: AirtableRecord[] = [];

    selectedPackage: AirtableRecord|null = null;
    listenerToUnload!: EventListenerWrapper;

    showForm: boolean = false;
    loading: boolean = true;

    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
    @AutoWired(BarcodeExternalNative) accessor barcodeExternalNative!: BarcodeExternalNative;
    @AutoWired(BarcodeScannerManager) accessor barcodeScannerManager!: BarcodeScannerManager;

    beforeMount() {
        this.sidebarStateListener.setHiddenSidebar(true);
        this.sidebarStateListener.setHiddenTopBar(true);

        this.listenerToUnload = this.barcodeExternalNative.on('allRead', (data) => this.barcodeScannerManager.callback(data));

        this.setupListener();
    }

    async mounted() {
        await this.loadTranslations();
        await this.loadPackages()

        this.loading = false;
    }


    unmounted() {
        this.barcodeExternalNative.off('allRead', this.listenerToUnload.listener);
    }

    setupListener() {
        this.barcodeScannerManager.customCallback = this.barCodeRead;
    }

    async loadPackages() {
        this.loading = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.existingPackages = json.records;

            this.sortPackages();
        }

        this.loading = false;
    }

    async loadTranslations() {
        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objectNames?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.objectTranslations = json.records;
        }
    }

    sortPackages() {
        this.existingPackages.sort((a: AirtableRecord, b: AirtableRecord) => {
            // Sort by creatingDate desc and validated asc
            if(a.fields.validated === b.fields.validated) {
                return new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime();
            } else {
                return a.fields.validated ? 1 : -1;
            }
        })
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        // Find package base on return then shipping
        const correspondingAirtablePackage = this.existingPackages.find((packageData) => packageData.fields.return === data.content || packageData.fields.shipping === data.content);
        if(correspondingAirtablePackage) {
            this.selectedPackage = correspondingAirtablePackage;
        }
    }

    createdPackage(packageData: AirtableRecord) {
        this.existingPackages.push(packageData);

        this.sortPackages();
    }

    updatedPackage(updatedPackageData: AirtableRecord) {
        const index = this.existingPackages.findIndex((packageData) => packageData.id === updatedPackageData.id);
        if(index !== -1) this.existingPackages.splice(index, 1, updatedPackageData);

        this.sortPackages();
    }

    askCamera() {
        const video: any = document.getElementById('video');
        async function startCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
            } catch (err) {
                console.error("Error accessing the camera", err);
            }
        }
        startCamera();
    }
}